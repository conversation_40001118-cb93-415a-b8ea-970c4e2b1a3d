openapi: 3.0.4
info:
  title: Server Variables - Two
  description: sample OAS 3 definition to test server variables with urls
  version: 1.0.0
servers:
- url: "https://localhost:3200{basePath}"
  variables:
    basePath:
      default: "/twoFirstUrl"
- url: "http://localhost:3201{basePath}"
  variables:
    basePath:
      default: "/twoSecondUrl"
paths:
  /b:
    post:
      summary: simple service B
      requestBody:
        content:
          'application/json':
            schema:
              properties:
                foo:
                  type: string
                bar:
                  type: string
              required:
                - foo
              type: object
        required: true
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: 'string'
