openapi: 3.1.0
info:
  title: Test
  description: 'Test'
  license:
    name: 'Apache 2.0'
    url: 'https://www.apache.org/licenses/LICENSE-2.0.html'
  version: '1.0'
servers:
  - url: http://petstore.swagger.io/v1
components:
  schemas:
    Pet:
      title: My Pet
      type: object
      required:
        - id
        - name
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
        tag:
          type: string
      discriminator:
        propertyName: id
        mapping:
        x-primitiveExtension: 1
        x-arrayExtension:
          - 2
        x-objectExtension:
          prop: 3
        default: default value
    Object:
      type: object
      externalDocs:
        description: Object Docs
        url: http://swagger.io
        x-primitiveExtension: 1
        x-arrayExtension:
          - 2
        x-objectExtension:
          prop: 3
        default: default value
      properties:
        name:
          type: string
    Book:
      type: object
      properties:
        id:
          type: integer
        title:
          type: string
        author:
          type: string
      xml:
        prefix: "smp"
        namespace: "http://example.com/schema"
        x-primitiveExtension: 1
        x-arrayExtension:
          - 2
        x-objectExtension:
          prop: 3

