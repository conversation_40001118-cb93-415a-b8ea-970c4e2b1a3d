openapi: 3.0.4
info:
  title: "Schema in Parameters"
  description: |-
    This document has examples for examining the `schema` within a set of parameters
    * String Enum (/pet/findByStatus)
    * Array of Strings (/pet/findByTags)
    * Array of Boolean (/petOwner/listOfServiceTrainer)
    * Array of Objects (/petOwners/createWithList)
    * Array of Enum (/petOwner/findByPreference)

    This document also covers a debounce test for `schema` type `string
    * String (/petOwner)

    This documents does not cover:
    * Array of Arrays

    Additional notes
    * Provides additional coverage and examples not covered in the Multiple Examples Core Document (Test)
    * Code component reference `JsonSchemaForm`
    * `pet` and `tag` schemas are reduced from Swagger Petstore
  version: "1.0.0"
paths:
  /pet/findByStatus:
    get:
      summary: Finds Pets by status
      description: Multiple status values can be provided with comma separated strings
      operationId: findPetsByStatus
      parameters:
       - name: status
         in: query
         description: Status values that need to be considered for filter
         required: false
         explode: true
         schema:
            type: string
            enum:
             - available
             - pending
             - sold
            default: available
      responses:
        '200':
          description: successful operation
          content:
            application/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Pet'
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Pet'
        '400':
          description: Invalid status value
      security:
        - petstore_auth:
            - 'write:pets'
            - 'read:pets'
  /pet/findByTags:
    get:
      tags:
        - pet
      summary: Finds Pets by tags
      description: >-
        Multiple tags can be provided with comma separated strings. Use tag1,
        tag2, tag3 for testing.
      operationId: findPetsByTags
      parameters:
        - name: tags
          in: query
          description: Tags to filter by
          required: false
          explode: true
          schema:
            type: array
            items:
              type: string
      responses:
        '200':
          description: successful operation
          content:
            application/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Pet'
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Pet'
        '400':
          description: Invalid tag value
      security:
        - petstore_auth:
            - 'write:pets'
            - 'read:pets'
  '/petOwner/{petOwnerId}':
    get:
      tags:
        - petOwner
      summary: Find pet owner by ID
      description: Returns a single pet owner if ID found, list if no ID provided
      operationId: getPetOwnerById
      parameters:
        - name: petOwnerId
          in: path
          description: ID of pet owner to return
          required: false
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: successful operation
          content:
            application/xml:
              schema:
                $ref: '#/components/schemas/PetOwner'
            application/json:
              schema:
                $ref: '#/components/schemas/PetOwner'
        '400':
          description: Invalid ID supplied
        '404':
          description: Pet not found
  /petOwner/listOfServiceTrainer:
    get:
      tags:
        - petOwner
      summary: List of Service Trainers
      description: >-
        Expect boolean, but allow both true and false
      operationId: listOfServiceTrainer
      parameters:
        - name: tags
          in: query
          description: Boolean to filter by
          required: false
          explode: true
          schema:
            type: array
            items:
              type: boolean
      responses:
        '200':
          description: successful operation
          content:
            application/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PetOwner'
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PetOwner'
        '400':
          description: Invalid tag value
  /petOwner/findByPreference:
    get:
      tags:
        - petOwner
      summary: Find by Pet Owner Preferences
      description: >-
        Expect enum
      operationId: findByPreference
      parameters:
        - name: tags
          in: query
          description: Enum to filter by
          required: false
          explode: true
          schema:
            type: array
            items:
              type: string
              enum:
              - dog
              - cat
              - bird
              - fish
              - other
              default: dog
      responses:
        '200':
          description: successful operation
          content:
            application/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PetOwner'
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PetOwner'
        '400':
          description: Invalid tag value
          components:
  /petOwner/createWithList:
    post:
      tags:
        - petOwner
      summary: Creates list of pet owners with given input array
      operationId: petOwnerCreateWithList
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/PetOwner'
      responses:
        '200':
          description: successful operation
          content:
            application/xml:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PetOwner'
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PetOwner'
        '400':
          description: Invalid values
  schemas:
    Pet:
      x-swagger-router-model: io.swagger.petstore.model.Pet
      required:
        - name
        - photoUrls
      properties:
        id:
          type: integer
          format: int64
          example: 10
        name:
          type: string
          example: doggie
        # remove category property
        petOwners:
         type: array
         items:
           $ref: '#/components/schemas/PetOwner'
        photoUrls:
          type: array
          xml:
            wrapped: true
          items:
            type: string
            xml:
              name: photoUrl
        tags:
          type: array
          xml:
            wrapped: true
          items:
            $ref: '#/components/schemas/Tag'
            xml:
              name: tag
        status:
          type: string
          description: pet status in the store
          enum:
            - available
            - pending
            - sold
      xml:
        name: pet
      type: object
    # remove ApiResponse
    PetOwner:
      type: "object"
      properties:
        id:
          type: "integer"
          format: "int64"
          example: 10
        petId:
          type: "integer"
          format: "int64"
          example: 201
        petOwnerFirstName:
          type: "string"
          example: "John"
        petOwnerLastName:
          type: "string"
          example: "Smith"
        petOwnerContact:
          type: "string"
          example: "<EMAIL>"
        petOwnerStatus:
          type: "integer"
          format: "int32"
          description: "Pet Owner Status"
          example: 302
        petOwnerPreferences:
          type: "string"
          description: "Pet Owner Preferred Pet Types"
          enum:
          - "dog"
          - "cat"
          - "bird"
          - "fish"
          - "other"
        petOwnerServiceTrainer:
          type: "boolean"
          description: "Pet Onwer is Service Trainer"
          default: false
    Tag:
      x-swagger-router-model: io.swagger.petstore.model.Tag
      properties:
        id:
          type: integer
          format: int64
        name:
          type: string
      xml:
        name: tag
      type: object
  requestBodies:
    Pet:
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Pet'
        application/xml:
          schema:
            $ref: '#/components/schemas/Pet'
      description: Pet object that needs to be added to the store
