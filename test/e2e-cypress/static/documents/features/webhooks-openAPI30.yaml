openapi: 3.0.4
info:
  title: Webhooks does not exist in OpenAPI 3.0 and should not render
  version: 1.0.0
  summary: a new 3.1.x specific field for summary
  description: This is a sample server for a pet store.
  termsOfService: https://example.com/terms/
webhooks:
  newPet:
    post:
      summary: summary for newPet--post
      requestBody:
        description: Information about a new pet in the system
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/foo"
      responses:
        "200":
          description: Return a 200 status to indicate that the data was received successfully
components:
  schemas:
    foo:
      type: string
