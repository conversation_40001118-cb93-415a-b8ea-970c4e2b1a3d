openapi: "3.0.4"

paths:
  /aev:
    get:
      parameters:
      - name: param
        in: query
        allowEmptyValue: true
        schema:
          type: string
      responses:
        200:
          description: ok
  /aev/and/required:
    get:
      parameters:
      - name: param
        in: query
        allowEmptyValue: true
        required: true
        schema:
          type: string
      responses:
        200:
          description: ok
