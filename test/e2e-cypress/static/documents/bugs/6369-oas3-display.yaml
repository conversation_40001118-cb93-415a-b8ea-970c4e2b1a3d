openapi: 3.0.4
info:
  title: "OAS 3.0 sample with Object Model deprecated: true"
  version: 0.1.0
servers:
  - url: http://testserver1.com
paths:
  /test/:
    get:
      responses:
        '200':
          description: Successful Response
components:
  schemas:
    IdentificationProfile:
      type: object
      deprecated: true
      properties:
        code:
          type: integer
          format: int32
        message:
          type: string
    IDP2:
      type: array
      deprecated: true
      items:
        type: string
