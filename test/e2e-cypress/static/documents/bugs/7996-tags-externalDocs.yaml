openapi: 3.0.4
info:
  title: "This is an example highlighting a display issue related to `tags` and `description` entries therein. It contains tag descriptions with & without externalDocs."
  version: 1.0.0
tags:
- name: "foo"
  description: "Foo: Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex\
 ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum."
  externalDocs:
    description: "Find out more"
    url: "http://swagger.io"
- name: "bar"
  description: "Bar: Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex \
ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum."
- name: "baz"
  description: "Baz: has less description text"
  externalDocs:
    description: "Find out more about our store"
    url: "http://swagger.io"
paths:
  /foobar:
    get:
      tags:
      - "foo"
      - "bar"
      responses:
        '200':
          description: OK
