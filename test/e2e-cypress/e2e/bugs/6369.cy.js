describe("#6369: Object model render of field: deprecated", () => {
  describe("OAS3", () => {
    it("should display row with td:deprecated when set to true", () => {
      cy.visit("/?url=/documents/bugs/6369-oas3-display.yaml")
        .get("#model-IdentificationProfile > .model-box")
        .click()
        .get("#model-IdentificationProfile .model-box .model .inner-object table")
        .find("tr")
        .should("have.length", 3)
        .contains("td", "deprecated")
    })
    it("should not display row with td:deprecated when set to false", () => {
      cy.visit("/?url=/documents/bugs/6369-oas3-no-display.yaml")
        .get("#model-IdentificationProfile > .model-box")
        .click()
        .get("#model-IdentificationProfile .model-box .model .inner-object table")
        .find("tr")
        .should("have.length", 2)
        .get("#model-IdentificationProfile .model-box .model .inner-object table")
        .find(("td:contains(\"deprecated\")"))
        .should("not.exist")
    })
  })
  describe ("OAS2", () => {
    it("should display row with td:deprecated when set to true", () => {
      cy.visit("/?url=/documents/bugs/6369-oas2-display.yaml")
        .get("#model-IdentificationProfile > .model-box")
        .click()
        .get("#model-IdentificationProfile .model-box .model .inner-object")
        .contains("td", "deprecated")
    })
    it("should not display row with td:deprecated when set to false", () => {
      cy.visit("/?url=/documents/bugs/6369-oas2-no-display.yaml")
        .get("#model-IdentificationProfile > .model-box")
        .click()
        .get("#model-IdentificationProfile .model-box .model .inner-object table")
        .find("tr")
        .should("have.length", 2)
        .get("#model-IdentificationProfile .model-box .model .inner-object table")
        .find(("td:contains(\"deprecated\")"))
        .should("not.exist")
    })
  })
})
