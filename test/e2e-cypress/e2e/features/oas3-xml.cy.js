describe("XML schema rendering examples", () => {
  it("Should render RequestBody example value when schema contains `oneOf` for mediaType `text/xml`", () => {
    cy.visit("?url=/documents/features/oas3-xml.json")
      .get("#operations-default-post_foo")
      .click()
      .get("label > .content-type-wrapper > .content-type option:selected")
      .should("have.text", "text/xml")
      .get(".body-param__example")
      .should("contains.text", "<fooOne>")
  })
  it("Should render RequestBody example value when schema contains `anyOf` for mediaType `text/xml`", () => {
    cy.visit("?url=/documents/features/oas3-xml.json")
      .get("#operations-default-post_bar")
      .click()
      .get("label > .content-type-wrapper > .content-type option:selected")
      .should("have.text", "text/xml")
      .get(".body-param__example")
      .should("contains.text", "<fooOne>")
  })
  it("Should render RequestBody example value when schema contains `oneOf` for mediaType `application/xml`", () => {
    cy.visit("?url=/documents/features/oas3-xml.json")
      .get("#operations-default-post_foobar")
      .click()
      .get("label > .content-type-wrapper > .content-type option:selected")
      .should("have.text", "application/xml")
      .get(".body-param__example")
      .should("contains.text", "<fooOne>")
  })
  it("Should render RequestBody example value when schema contains `anyOf` for mediaType `application/xml`", () => {
    cy.visit("?url=/documents/features/oas3-xml.json")
      .get("#operations-default-post_barfoo")
      .click()
      .get("label > .content-type-wrapper > .content-type option:selected")
      .should("have.text", "application/xml")
      .get(".body-param__example")
      .should("contains.text", "<fooOne>")
  })
  it("Should render RequestBody example value when switching mediaType to `text/xml` with singular content schema", () => {
    cy.visit("?url=/documents/features/oas3-xml.json")
      .get("#operations-default-post_barfoo")
      .click()
      .get("label > .content-type-wrapper > .content-type")
      .select("text/xml")
      .get(".body-param__example")
      .should("contains.text", "<fooThree>")
  })
  it("Should render RequestBody example value when switching mediaType to `application/xml` with singular content schema", () => {
    cy.visit("?url=/documents/features/oas3-xml.json")
      .get("#operations-default-post_foo")
      .click()
      .get("label > .content-type-wrapper > .content-type")
      .select("application/xml")
      .get(".body-param__example")
      .should("contains.text", "<fooTwo>")
  })
  it("Should render Response example value for mediaType `application/xml`", () => {
    cy.visit("?url=/documents/features/oas3-xml.json")
      .get("#operations-default-post_foo")
      .click()
      .get(".response-col_description > .model-example")
      .should("contains.text", "<foobarResObj>")
  })
  it("Should render Response example value for mediaType `text/xml`", () => {
    cy.visit("?url=/documents/features/oas3-xml.json")
      .get("#operations-default-post_foobar")
      .click()
      .get(".response-col_description > .model-example")
      .should("contains.text", "<foobarResObj>")
  })
})
