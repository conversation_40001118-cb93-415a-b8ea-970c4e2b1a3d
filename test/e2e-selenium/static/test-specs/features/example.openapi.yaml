openapi: 3.0.4
servers:
  - url: http://example.com/v1
    description: Production server version 1
  - url: http://staging-api.example.com
    description: Staging server
info:
  description: |
    This is an API documentation of example.
  version: "0.1.0"
  title: Example
  termsOfService: 'http://www.example.com/terms/'
  contact:
    email: <EMAIL>
  license:
    name: Proprietary license
    url: 'http://www.example.com/license/'
tags:
  - name: agent
    description: Access to example
paths:
  /agents/{agentId}:
    put:
      tags:
        - agent
      summary: Edit agent
      operationId: editAgent
      parameters:
        - in: path
          name: agentId
          schema:
            type: integer
          example: 12345
          required: true
          description: Numeric ID of the paper agent to edit
      requestBody:
        required: true
        content:
          application/json_media-type-level:
            schema:
              type: object
              properties:
                code:
                  type: string
                name:
                  type: string
            example:
              code: AE1
              name: Andrew
          application/json_schema-level:
            schema:
              type: object
              properties:
                code:
                  type: string
                name:
                  type: string
              example:
                code: AE1
                name: Andrew
          application/json_property-level:
            schema:
              type: object
              properties:
                code:
                  type: string
                  example: AE1
                name:
                  type: string
                  example: Andrew
      responses:
        '200':
          description: media type-level example
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    format: int64
                  payload:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          format: int64
                          readOnly: true
                        code:
                          type: string
                        name:
                          type: string
              example:
                code: 200
                payload:
                - id: 1
                  code: AE2
                  name: Yono
        '201':
          description: schema-level example
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    format: int64
                  payload:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          format: int64
                          readOnly: true
                        code:
                          type: string
                        name:
                          type: string
                example:
                  code: 201
                  payload:
                  - id: 1
                    code: AE2
                    name: Yono
        '202':
          description: property-level example
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    format: int64
                    example: 202
                  payload:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          format: int64
                          readOnly: true
                          example: 1
                        code:
                          type: string
                          example: AE2
                        name:
                          type: string
                          example: Yono
