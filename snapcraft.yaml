name: swagger-ui
version: master
summary: The World's Most Popular API Framework
description: |
  Swagger UI is part of the Swagger project. The Swagger project allows you to
  produce, visualize and consume your OWN RESTful services. No proxy or 3rd
  party services required. Do it your own way.

  Swagger UI is a dependency-free collection of HTML, Javascript, and CSS
  assets that dynamically generate beautiful documentation and sandbox from a
  Swagger-compliant API. Because Swagger UI has no dependencies, you can host
  it in any server environment, or on your local machine.

grade: devel
confinement: strict

apps:
  swagger-ui:
    command: sh -c \"cd $SNAP/lib/node_modules/swagger-ui/dist && http-server -a localhost -p 8080\"
    daemon: simple
    plugs: [network, network-bind]

parts:
  swagger-ui:
    source: .
    plugin: nodejs
    npm-run: [build]
    node-packages: [handlebars, http-server]
