@use "variables" as *;
@use "type";

.errors-wrapper {
  margin: 20px;
  padding: 10px 20px;

  animation: scaleUp 0.5s;

  border: 2px solid $color-delete;
  border-radius: 4px;
  background: rgba($color-delete, 0.1);

  .error-wrapper {
    margin: 0 0 10px 0;
  }

  .errors {
    h4 {
      font-size: 14px;

      margin: 0;

      @include type.text_code();
    }

    small {
      color: $errors-wrapper-errors-small-font-color;
    }

    .message {
      white-space: pre-line;

      &.thrown {
        max-width: 100%;
      }
    }

    .error-line {
      text-decoration: underline;
      cursor: pointer;
    }
  }

  hgroup {
    display: flex;

    align-items: center;

    h4 {
      font-size: 20px;

      margin: 0;

      flex: 1;
      @include type.text_headline();
    }
  }
}

@keyframes scaleUp {
  0% {
    transform: scale(0.8);

    opacity: 0;
  }
  100% {
    transform: scale(1);

    opacity: 1;
  }
}
