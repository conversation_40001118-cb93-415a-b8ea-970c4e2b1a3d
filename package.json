{"name": "swagger-ui", "version": "5.26.0", "main": "./dist/swagger-ui.js", "module": "./dist/swagger-ui-es-bundle-core.js", "exports": {"./dist/swagger-ui.css": "./dist/swagger-ui.css", "./dist/oauth2-redirect.html": "./dist/oauth2-redirect.html", "./dist/swagger-ui-standalone-preset": "./dist/swagger-ui-standalone-preset.js", ".": {"browser": {"import": "./dist/swagger-ui-es-bundle-core.js", "require": "./dist/swagger-ui.js"}, "node": {"import": "./dist/swagger-ui-bundle.js", "require": "./dist/swagger-ui-es-bundle.js"}, "default": {"import": "./dist/swagger-ui-bundle.js", "require": "./dist/swagger-ui-es-bundle.js"}}}, "homepage": "https://github.com/swagger-api/swagger-ui", "repository": "**************:swagger-api/swagger-ui.git", "contributors": ["(in alphabetical order)", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"], "license": "Apache-2.0", "scripts": {"automated-release": "release-it -VV --config ./release/.release-it.json", "build": "npm run build-stylesheets && rimraf ./dist/swagger-ui.js ./dist/swagger-ui.js.map && npm run build-all-bundles", "build-all-bundles": "run-p --aggregate-output build:core build:bundle build:standalone build:es:bundle build:es:bundle:core", "build-stylesheets": "cross-env NODE_ENV=production BABEL_ENV=production BROWSERSLIST_ENV=browser-production webpack --color --config webpack/stylesheets.js && shx sed -i \"s/\\*zoom/zoom/g\" ./dist/swagger-ui.css", "build:bundle": "cross-env NODE_ENV=production BABEL_ENV=production BROWSERSLIST_ENV=isomorphic-production webpack --color --config webpack/bundle.js", "build:core": "cross-env NODE_ENV=production BABEL_ENV=production BROWSERSLIST_ENV=browser-production webpack --color --config webpack/core.js", "build:standalone": "cross-env NODE_ENV=production BABEL_ENV=production BROWSERSLIST_ENV=browser-production webpack --color --config webpack/standalone.js", "build:es:bundle": "cross-env NODE_ENV=production BABEL_ENV=production BROWSERSLIST_ENV=isomorphic-production webpack --color --config webpack/es-bundle.js", "build:es:bundle:core": "cross-env NODE_ENV=production BABEL_ENV=esm BROWSERSLIST_ENV=browser-production webpack --color --config webpack/es-bundle-core.js", "clean": "rimraf ./dist", "dev": "cross-env NODE_ENV=development BABEL_ENV=development BROWSERSLIST_ENV=browser-development webpack serve --config webpack/dev.js", "deps-license": "license-checker --production --csv --out $npm_package_config_deps_check_dir/licenses.csv && license-checker --development --csv --out $npm_package_config_deps_check_dir/licenses-dev.csv", "deps-size": "cross-env NODE_ENV=development webpack -p --config webpack/bundle.js --json | webpack-bundle-size-analyzer >| $npm_package_config_deps_check_dir/sizes.txt", "deps-check": "run-s deps-license deps-size", "lint": "eslint --ext \".js,.jsx\" src test dev-helpers flavors", "lint-errors": "eslint --quiet --ext \".js,.jsx\" src test dev-helpers flavors", "lint-fix": "eslint --ext \".js,.jsx\" src test dev-helpers flavors --fix", "lint-styles": "stylelint \"**/*.scss\"", "lint-styles-fix": "stylelint \"**/*.scss\" --fix", "test": "run-s lint-errors test:unit cy:ci", "test:artifact": "cross-env NODE_ENV=production BABEL_ENV=commonjs BROWSERSLIST_ENV=node-development jest --config ./config/jest/jest.artifact.config.js", "test:unit": "cross-env NODE_ENV=test BABEL_ENV=commonjs BROWSERSLIST_ENV=node-development jest --config ./config/jest/jest.unit.config.js", "cy:mock-api": "json-server --watch test/e2e-selenium/db.json --port 3204", "cy:server": "cross-env NODE_ENV=production BABEL_ENV=production BROWSERSLIST_ENV=browser-production webpack serve --config webpack/dev-e2e.js", "cy:start": "run-p -r cy:server cy:mock-api", "cy:open": "cross-env BROWSERSLIST_ENV=browser-production cypress open", "cy:run": "cross-env BROWSERSLIST_ENV=browser-production cypress run", "cy:ci": "start-server-and-test cy:start http://localhost:3204 cy:run", "cy:dev": "start-server-and-test cy:start http://localhost:3204 cy:open", "open-static": "node -e \"require('open')('http://localhost:3002')\"", "security-audit": "run-s -sc security-audit:all security-audit:prod", "security-audit:prod": "npm-audit-ci-wrapper -p -t low", "security-audit:all": "npm-audit-ci-wrapper -t moderate", "serve-static": "ws -d dist/ --hostname 0.0.0.0 -p 3002", "start": "npm-run-all --parallel serve-static open-static"}, "dependencies": {"@babel/runtime-corejs3": "^7.27.1", "@scarf/scarf": "=1.4.0", "base64-js": "^1.5.1", "classnames": "^2.5.1", "css.escape": "1.5.1", "deep-extend": "0.6.0", "dompurify": "=3.2.4", "ieee754": "^1.2.1", "immutable": "^3.x.x", "js-file-download": "^0.4.12", "js-yaml": "=4.1.0", "lodash": "^4.17.21", "prop-types": "^15.8.1", "randexp": "^0.5.3", "randombytes": "^2.1.0", "react": ">=16.8.0 <19", "react-copy-to-clipboard": "5.1.0", "react-debounce-input": "=3.3.0", "react-dom": ">=16.8.0 <19", "react-immutable-proptypes": "2.2.0", "react-immutable-pure-component": "^2.2.0", "react-inspector": "^6.0.1", "react-redux": "^9.2.0", "react-syntax-highlighter": "^15.6.1", "redux": "^5.0.1", "redux-immutable": "^4.0.0", "remarkable": "^2.0.1", "reselect": "^5.1.1", "serialize-error": "^8.1.0", "sha.js": "^2.4.11", "swagger-client": "^3.35.5", "url-parse": "^1.5.10", "xml": "=1.0.1", "xml-but-prettier": "^1.0.1", "zenscroll": "^4.0.2"}, "devDependencies": {"@babel/cli": "=7.26.4", "@babel/core": "=7.26.10", "@babel/eslint-parser": "=7.26.10", "@babel/plugin-transform-runtime": "=7.26.10", "@babel/preset-env": "=7.27.2", "@babel/preset-react": "=7.26.3", "@babel/register": "=7.27.1", "@cfaester/enzyme-adapter-react-18": "=0.8.0", "@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@jest/globals": "=29.7.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.6.0", "@release-it/conventional-changelog": "=8.0.2", "@svgr/webpack": "=8.1.0", "autoprefixer": "^10.4.21", "babel-loader": "^9.2.1", "babel-plugin-lodash": "=3.3.4", "babel-plugin-module-resolver": "=5.0.2", "babel-plugin-transform-react-remove-prop-types": "=0.4.24", "body-parser": "^1.19.0", "buffer": "^6.0.3", "cheerio": "=1.0.0", "cors": "^2.8.5", "cross-env": "=7.0.3", "css-loader": "=7.1.2", "cssnano": "=7.0.7", "cypress": "=14.2.0", "dedent": "^1.6.0", "deepmerge": "^4.3.1", "enzyme": "=3.11.0", "eslint": "^8.57.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest": "^28.12.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "esm": "=3.2.25", "expect": "=29.7.0", "express": "^4.21.2", "git-describe": "^4.1.0", "html-webpack-plugin": "^5.6.3", "html-webpack-skip-assets-plugin": "^1.0.4", "husky": "=9.1.7", "inspectpack": "=4.7.1", "jest": "=29.7.0", "jest-environment-jsdom": "=29.7.0", "jest-transform-stub": "=2.0.0", "jsdom": "=26.0.0", "json-loader": "^0.5.7", "json-merger": "^2.0.0", "json-server": "=0.17.4", "less": "^4.2.2", "license-checker": "^25.0.0", "lint-staged": "^15.5.0", "local-web-server": "^5.4.0", "mini-css-extract-plugin": "^2.9.2", "npm-audit-ci-wrapper": "^3.0.2", "npm-run-all": "^4.1.5", "oauth2-server": "^2.4.1", "open": "^10.1.0", "postcss": "^8.5.3", "postcss-scss": "^4.0.9", "postcss-loader": "^8.1.1", "postcss-preset-env": "^10.1.6", "prettier": "^3.5.3", "process": "^0.11.10", "react-refresh": "^0.17.0", "react-test-renderer": "^18.3.1", "release-it": "=17.11.0", "rimraf": "^6.0.1", "sass-embedded": "=1.86.0", "sass-loader": "^16.0.4", "shx": "=0.4.0", "sinon": "=20.0.0", "source-map-support": "^0.5.21", "start-server-and-test": "^2.0.11", "stream-browserify": "^3.0.0", "stylelint": "^16.19.1", "stylelint-prettier": "^5.0.3", "tachyons-sass": "^4.9.5", "terser-webpack-plugin": "^5.3.14", "webpack": "^5.97.1", "webpack-bundle-size-analyzer": "^3.1.0", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2", "webpack-node-externals": "=3.0.0", "webpack-stats-plugin": "=1.1.3"}, "overrides": {"@pmmmwh/react-refresh-webpack-plugin": {"webpack-dev-server": "$webpack-dev-server"}, "enzyme": {"cheerio": "=1.0.0-rc.12"}}, "config": {"deps_check_dir": ".deps_check"}}