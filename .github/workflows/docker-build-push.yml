name: <PERSON><PERSON> & Push SwaggerUI Docker image

on:
  workflow_run:
    workflows: ["Release SwaggerUI"]
    types:
      - completed
    branches: [master]

jobs:

  build-push:
    if: github.event.workflow_run.conclusion == 'success'
    name: Build & Push SwaggerUI Docker image
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Use Node.js 22
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: npm
          cache-dependency-path: package-lock.json

      - name: Install dependencies
        run: npm ci

      - name: Build SwaggerUI
        run: npm run build

      - name: Determine released version
        uses: actions/github-script@v7
        with:
          script: |
            const allArtifacts = await github.rest.actions.listWorkflowRunArtifacts({
               owner: context.repo.owner,
               repo: context.repo.repo,
               run_id: context.payload.workflow_run.id,
            });
            const matchArtifact = allArtifacts.data.artifacts.filter((artifact) => {
              return artifact.name == "released-version"
            })[0];
            const download = await github.rest.actions.downloadArtifact({
               owner: context.repo.owner,
               repo: context.repo.repo,
               artifact_id: matchArtifact.id,
               archive_format: 'zip',
            });
            const fs = require('fs');
            fs.writeFileSync('${{github.workspace}}/released-version.zip', Buffer.from(download.data));
      - run: |
          unzip released-version.zip
          RELEASED_VERSION=$(cat released-version.txt)
          echo "RELEASED_VERSION=$RELEASED_VERSION" >> $GITHUB_ENV

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_SB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_SB_PASSWORD }}

      - name: Build docker image and push
        uses: docker/build-push-action@v6
        with:
          context: .
          push: true
          platforms: linux/amd64,linux/arm/v6,linux/arm64,linux/386,linux/ppc64le
          provenance: false
          tags: swaggerapi/swagger-ui:latest,swaggerapi/swagger-ui:v${{ env.RELEASED_VERSION }}
