# This workflow will do a clean install of node dependencies, build the source code and run tests across different versions of node
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: Node.js CI

on:
  push:
    branches: [ master, next ]
  pull_request:
    branches: [ master, next ]

env:
  CYPRESS_CACHE_FOLDER: cypress/cache

permissions:
  contents: read

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Use Node.js 20.x
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Cache Node Modules and Cypress binary
        uses: actions/cache@v4
        id: cache-primes
        with:
          path: |
            node_modules
            ${{ env.CYPRESS_CACHE_FOLDER }}
          key: ${{ runner.os }}-node-and-cypress-${{ hashFiles('package-lock.json') }}

      - name: Install dependencies
        if: steps.cache-primes.outputs.cache-hit != 'true'
        run: npm ci

      - name: Lint code for errors only
        run: npm run lint-errors

      - name: Run all tests
        run: npm run test:unit
        env:
          CI: true

      - name: Build SwaggerUI
        run: npm run build

      - name: Test build artifacts
        run: npm run test:artifact

  e2e-tests:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        containers: ['+(a11y|security|bugs)/**/*cy.js', 'features/**/+(o|d)*.cy.js', 'features/**/m*.cy.js', 'features/**/!(o|d|m)*.cy.js']

    steps:
      - uses: actions/checkout@v4

      - name: Use Node.js 20.x
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Cache Node Modules and Cypress binary
        uses: actions/cache@v4
        id: cache-primes
        with:
          path: |
            node_modules
            ${{ env.CYPRESS_CACHE_FOLDER }}
          key: ${{ runner.os }}-node-and-cypress-${{ hashFiles('package-lock.json') }}

      - name: Install dependencies
        if: steps.cache-primes.outputs.cache-hit != 'true'
        run: npm ci

      - name: Cypress Test
        run: npx start-server-and-test cy:start http://localhost:3204 'npm run cy:run -- --spec "test/e2e-cypress/e2e/${{ matrix.containers }}"'
