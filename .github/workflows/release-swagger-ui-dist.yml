name: Release SwaggerUI Dist

on:
  workflow_run:
    workflows: ["Release SwaggerUI"]
    types:
      - completed
    branches: [ master ]

jobs:
  release-swagger-ui-dist:
    if: github.event.workflow_run.conclusion == 'success'
    name: Release swagger-ui-dist npm package
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          persist-credentials: false
          ref: master

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: npm
          cache-dependency-path: package-lock.json

      - name: Install dependencies
        run: npm ci

      - name: Download build artifact
        uses: actions/github-script@v7
        with:
          script: |
            const allArtifacts = await github.rest.actions.listWorkflowRunArtifacts({
               owner: context.repo.owner,
               repo: context.repo.repo,
               run_id: context.payload.workflow_run.id,
            });
            const matchArtifact = allArtifacts.data.artifacts.filter((artifact) => {
              return artifact.name == "dist"
            })[0];
            const download = await github.rest.actions.downloadArtifact({
               owner: context.repo.owner,
               repo: context.repo.repo,
               artifact_id: matchArtifact.id,
               archive_format: 'zip',
            });
            const fs = require('fs');
            fs.writeFileSync('${{github.workspace}}/dist.zip', Buffer.from(download.data));
      - run: |
          unzip -o dist.zip -d dist

      - name: Publish to npmjs.com
        run: ./deploy.sh
        working-directory: ./swagger-ui-dist-package
        env:
          NPM_TOKEN: ${{ secrets.NPM_TOKEN }}
          PUBLISH_DIST: true
