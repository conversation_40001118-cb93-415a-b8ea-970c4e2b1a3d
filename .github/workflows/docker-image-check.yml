name: Security scan for docker image

on:
  workflow_dispatch:
  schedule:
    - cron:  '30 4 * * *'

permissions:
  contents: read

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'docker.swagger.io/swaggerapi/swagger-ui:unstable'
          format: 'table'
          exit-code: '1'
          ignore-unfixed: true
          vuln-type: 'os,library'
          severity: 'CRITICAL,HIGH'
