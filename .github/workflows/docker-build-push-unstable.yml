name: <PERSON><PERSON> & Push SwaggerUI unstable Docker image

on:
  workflow_run:
    workflows: ["Node.js CI"]
    types:
      - completed
    branches: [master]

jobs:

  build-push-unstable:
    if: github.event.workflow_run.conclusion == 'success'
    name: Build & Push SwaggerUI unstable Docker image
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Use Node.js 22
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: npm
          cache-dependency-path: package-lock.json

      - name: Install dependencies
        run: npm ci

      - name: Build SwaggerUI
        run: npm run build

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_SB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_SB_PASSWORD }}

      - name: Build docker image and push
        uses: docker/build-push-action@v6
        with:
          context: .
          push: true
          platforms: linux/amd64,linux/arm/v6,linux/arm64,linux/386,linux/ppc64le
          provenance: false
          tags: swaggerapi/swagger-ui:unstable
