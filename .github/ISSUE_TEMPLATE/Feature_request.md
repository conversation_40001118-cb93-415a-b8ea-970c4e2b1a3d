---
name: Feature request
about: Suggest a new feature or enhancement for this project

---

 ### Content & configuration

Swagger/OpenAPI definition:
 ```yaml
 # your YAML here
 ```

 Swagger-UI configuration options:
 ```js
 SwaggerUI({
   // your config options here
 })
 ```

 ```
 ?yourQueryStringConfig
 ```


### Is your feature request related to a problem?
<!--
  Please provide a clear and concise description of what the problem is.
  "I'm always frustrated when..."
  -->

### Describe the solution you'd like
<!-- A clear and concise description of what you want to happen. -->

### Describe alternatives you've considered
<!--
  A clear and concise description of any alternative solutions or features
  you've considered.
-->

### Additional context
<!-- Add any other context or screenshots about the feature request here. -->
